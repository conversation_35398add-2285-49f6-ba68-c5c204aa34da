package it.circle.plugin.service.impl;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import it.circle.crm.data.account.document.AccountDocument;
import it.circle.plugin.model.FieldMetadata;
import it.circle.plugin.model.FieldsLinee;
import it.circle.plugin.model.Filter;
import it.circle.plugin.model.Target;
import it.circle.plugin.repository.AccountDocumentRepository;
import it.circle.plugin.service.AccountExporterService;
import it.circle.plugin.service.ExcelExportService;
import it.circle.plugin.service.FieldValidationService;
import it.circle.plugin.service.FieldMetadataService;
import it.circle.plugin.utility.QueryBuilderUtility;
import it.circle.plugin.utility.ExportUtility;
import it.circle.plugin.utility.ReflectionUtility;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.Decimal128;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.util.*;
import java.util.Date;

@Slf4j
@Service
@RequiredArgsConstructor
public class AccountExporterServiceImpl implements AccountExporterService {

    private final AccountDocumentRepository accountRepository;
    private final ExcelExportService excelExportService;
    private final MongoTemplate mongoTemplate;
    private final FieldValidationService fieldValidationService;
    private final FieldMetadataService fieldMetadataService;
    private final ReflectionUtility reflectionUtility;

    private final ObjectMapper objectMapper = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    @Override
    public void processAccountExport(String instanceId, String owner, Target target) {
        log.info("Processing Account export for instance: {}, owner: {}", instanceId, owner);

        // Safe logging for filters count
        int filtersCount = (target != null && target.getFilters() != null) ? target.getFilters().size() : 0;
        log.info("Target fields: {}, filters count: {}", target.getFields(), filtersCount);

        try {
            // Step 1: Handle filters - build query criteria
            List<Filter> filters = (target != null && target.getFilters() != null) ? target.getFilters() : null;

            Query query;
            if (filters == null || filters.isEmpty()) {
                log.info("No filters provided, fetching all accounts for instance: {}", instanceId);
                query = new Query();
                if (instanceId != null && !instanceId.isEmpty()) {
                    query.addCriteria(Criteria.where("instanceId").is(instanceId));
                }
            } else {
                log.info("Building query with {} filters", filters.size());
                query = QueryBuilderUtility.buildQuery(filters, instanceId);
            }

            log.info("Final MongoDB query: {}", query);

            // Step 2: Fetch account data from database
            List<AccountDocument> accounts = mongoTemplate.find(query, AccountDocument.class);

            // --- Field validation and filtering ---
            List<String> headerFields = target.getFields().getFieldsHeader();
            Map<String, FieldMetadata> accountMeta = fieldMetadataService.buildFieldMetadataMap("Account", instanceId);

            List<String> validHeaderFields = new ArrayList<>();
            for (String field : headerFields) {
                if (accountMeta.containsKey(field)) {
                    validHeaderFields.add(field);
                } else {
                    log.warn("Invalid field in fieldsHeader: {} — skipping", field);
                }
            }

            List<FieldsLinee> lineConfigs = target.getFields().getFieldsLinee();
            List<FieldsLinee> validLineConfigs = new ArrayList<>();

            for (FieldsLinee fieldsLinee : lineConfigs) {
                Map<String, FieldMetadata> subMeta = fieldMetadataService.buildFieldMetadataMap(fieldsLinee.getEntity(), instanceId);

                List<String> validLineFields = new ArrayList<>();
                for (String field : fieldsLinee.getFields()) {
                    if (subMeta.containsKey(field)) {
                        validLineFields.add(field);
                    } else {
                        log.warn("Invalid field in fieldsLinee for entity {}: {} — skipping", fieldsLinee.getEntity(), field);
                    }
                }

                if (!validLineFields.isEmpty()) {
                    validLineConfigs.add(new FieldsLinee(fieldsLinee.getEntity(), validLineFields));
                } else {
                    log.warn("No valid fields left for entity {} — skipping entire line config", fieldsLinee.getEntity());
                }
            }
            // --- End validation and filtering ---

            log.info("Found {} accounts matching criteria", accounts.size());

            if (accounts.isEmpty()) {
                log.warn("No accounts found for the given criteria!");
                ExportUtility.logExportSummary("Account", instanceId, owner, 0, "No accounts found");
                return;
            }

            // Step 3: Build field metadata map from SettingsEntity
            Map<String, FieldMetadata> fieldMetadataMap = fieldMetadataService.buildFieldMetadataMap("Account", instanceId);
            log.info("Built field metadata map with {} fields for Account entity", fieldMetadataMap.size());

            List<Map<String, Object>> exportData = new ArrayList<>();

            for (AccountDocument account : accounts) {
                // Header row
                Map<String, Object> headerMap = reflectionUtility.extractFields(account, validHeaderFields);

                // If there are no line configurations, just add the header data
                if (validLineConfigs.isEmpty()) {
                    exportData.add(headerMap);
                } else {
                    // For each line config (if any sub-entities exist for Account)
                    for (FieldsLinee fieldsLinee : validLineConfigs) {
                        String subFieldName = fieldsLinee.getEntity();
                        List<?> subRows = (List<?>) reflectionUtility.getFieldValue(account, subFieldName);
                        if (subRows != null) {
                            for (Object row : subRows) {
                                Map<String, Object> lineMap = reflectionUtility.extractFields(row, fieldsLinee.getFields());
                                Map<String, Object> combined = new LinkedHashMap<>(headerMap);
                                combined.putAll(lineMap);
                                exportData.add(combined);
                            }
                        }
                    }
                }
            }

            String fileName = ExportUtility.generateFileName("Account", instanceId, owner);
            ByteArrayOutputStream excelFile = excelExportService.generateExcelFile(
                    exportData, "Accounts", fileName
            );

            String projectDir = System.getProperty("user.dir");
            String exportPath = projectDir + "/exports";
            String savedPath = ExportUtility.saveExcelFile(excelFile, fileName, exportPath);
            log.info("Excel file saved in project directory: {}", savedPath);

            if (!exportData.isEmpty()) {
                Set<String> actualFields = exportData.get(0).keySet();
                log.info("Exported fields in Excel: {}", actualFields);
            }

            log.info("Successfully completed Account export for {} records with array expansion support", exportData.size());

        } catch (Exception e) {
            log.error("Error processing Account export for instance: {}", instanceId, e);
            throw new RuntimeException("Failed to process Account export", e);
        }
    }

}
