package it.circle.plugin.service.impl;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import it.circle.crm.data.venue.document.VenueDocument;
import it.circle.plugin.model.FieldMetadata;
import it.circle.plugin.model.FieldsLinee;
import it.circle.plugin.model.Filter;
import it.circle.plugin.model.Target;
import it.circle.plugin.repository.VenueDocumentRepository;
import it.circle.plugin.service.VenueExporterService;
import it.circle.plugin.service.ExcelExportService;
import it.circle.plugin.service.FieldValidationService;
import it.circle.plugin.service.FieldMetadataService;
import it.circle.plugin.utility.QueryBuilderUtility;
import it.circle.plugin.utility.ExportUtility;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.Decimal128;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.util.*;
import java.util.Date;

@Slf4j
@Service
@RequiredArgsConstructor
public class VenueExporterServiceImpl implements VenueExporterService {

    private final VenueDocumentRepository venueRepository;
    private final ExcelExportService excelExportService;
    private final MongoTemplate mongoTemplate;
    private final FieldValidationService fieldValidationService;
    private final FieldMetadataService fieldMetadataService;

    private final ObjectMapper objectMapper = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    @Override
    public void processVenueExport(String instanceId, String owner, Target target) {
        log.info("Processing Venue export for instance: {}, owner: {}", instanceId, owner);

        // Safe logging for filters count
        int filtersCount = (target != null && target.getFilters() != null) ? target.getFilters().size() : 0;
        log.info("Target fields: {}, filters count: {}", target.getFields(), filtersCount);

        try {
            // Step 1: Handle filters - build query criteria
            List<Filter> filters = (target != null && target.getFilters() != null) ? target.getFilters() : null;

            Query query;
            if (filters == null || filters.isEmpty()) {
                log.info("No filters provided, fetching all venues for instance: {}", instanceId);
                query = new Query();
                if (instanceId != null && !instanceId.isEmpty()) {
                    query.addCriteria(Criteria.where("instanceId").is(instanceId));
                }
            } else {
                log.info("Building query with {} filters", filters.size());
                query = QueryBuilderUtility.buildQuery(filters, instanceId);
            }

            log.info("Final MongoDB query: {}", query);

            // Step 2: Fetch venue data from database
            List<VenueDocument> venues = mongoTemplate.find(query, VenueDocument.class);

            // --- Field validation and filtering ---
            List<String> headerFields = target.getFields().getFieldsHeader();
            Map<String, FieldMetadata> venueMeta = fieldMetadataService.buildFieldMetadataMap("Venue", instanceId);

            List<String> validHeaderFields = new ArrayList<>();
            for (String field : headerFields) {
                if (venueMeta.containsKey(field)) {
                    validHeaderFields.add(field);
                } else {
                    log.warn("Invalid field in fieldsHeader: {} — skipping", field);
                }
            }

            List<FieldsLinee> lineConfigs = target.getFields().getFieldsLinee();
            List<FieldsLinee> validLineConfigs = new ArrayList<>();

            for (FieldsLinee fieldsLinee : lineConfigs) {
                Map<String, FieldMetadata> subMeta = fieldMetadataService.buildFieldMetadataMap(fieldsLinee.getEntity(), instanceId);

                List<String> validLineFields = new ArrayList<>();
                for (String field : fieldsLinee.getFields()) {
                    if (subMeta.containsKey(field)) {
                        validLineFields.add(field);
                    } else {
                        log.warn("Invalid field in fieldsLinee for entity {}: {} — skipping", fieldsLinee.getEntity(), field);
                    }
                }

                if (!validLineFields.isEmpty()) {
                    validLineConfigs.add(new FieldsLinee(fieldsLinee.getEntity(), validLineFields));
                } else {
                    log.warn("No valid fields left for entity {} — skipping entire line config", fieldsLinee.getEntity());
                }
            }
            // --- End validation and filtering ---

            log.info("Found {} venues matching criteria", venues.size());

            if (venues.isEmpty()) {
                log.warn("No venues found for the given criteria!");
                ExportUtility.logExportSummary("Venue", instanceId, owner, 0, "No venues found");
                return;
            }

            // Step 3: Build field metadata map from SettingsEntity
            Map<String, FieldMetadata> fieldMetadataMap = fieldMetadataService.buildFieldMetadataMap("Venue", instanceId);
            log.info("Built field metadata map with {} fields for Venue entity", fieldMetadataMap.size());

            List<Map<String, Object>> exportData = new ArrayList<>();

            for (VenueDocument venue : venues) {
                // Header row
                Map<String, Object> headerMap = extractFields(venue, validHeaderFields);

                // If there are no line configurations, just add the header data
                if (validLineConfigs.isEmpty()) {
                    exportData.add(headerMap);
                } else {
                    // For each line config (if any sub-entities exist for Venue)
                    for (FieldsLinee fieldsLinee : validLineConfigs) {
                        String subFieldName = fieldsLinee.getEntity();
                        List<?> subRows = (List<?>) getFieldValue(venue, subFieldName);
                        if (subRows != null) {
                            for (Object row : subRows) {
                                Map<String, Object> lineMap = extractFields(row, fieldsLinee.getFields());
                                Map<String, Object> combined = new LinkedHashMap<>(headerMap);
                                combined.putAll(lineMap);
                                exportData.add(combined);
                            }
                        }
                    }
                }
            }

            String fileName = ExportUtility.generateFileName("Venue", instanceId, owner);
            ByteArrayOutputStream excelFile = excelExportService.generateExcelFile(
                    exportData, "Venues", fileName
            );

            String projectDir = System.getProperty("user.dir");
            String exportPath = projectDir + "/exports";
            String savedPath = ExportUtility.saveExcelFile(excelFile, fileName, exportPath);
            log.info("Excel file saved in project directory: {}", savedPath);

            if (!exportData.isEmpty()) {
                Set<String> actualFields = exportData.get(0).keySet();
                log.info("Exported fields in Excel: {}", actualFields);
            }

            log.info("Successfully completed Venue export for {} records with array expansion support", exportData.size());

        } catch (Exception e) {
            log.error("Error processing Venue export for instance: {}", instanceId, e);
            throw new RuntimeException("Failed to process Venue export", e);
        }
    }

    private Map<String, Object> extractFields(Object obj, List<String> fields) {
        Map<String, Object> map = new LinkedHashMap<>();
        for (String fieldName : fields) {
            Object value = getFieldValue(obj, fieldName);
            map.put(fieldName, value != null ? value : "");
        }
        return map;
    }

    private Object getFieldValue(Object obj, String fieldName) {
        try {
            // First try to get value using reflection with @Field annotation support
            Object value = getFieldValueWithReflection(obj, fieldName);
            if (value != null) {
                return value;
            }
            return value;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Get field value using reflection, handling @Field annotations properly
     */
    private Object getFieldValueWithReflection(Object obj, String fieldName) {
        if (obj == null || fieldName == null) {
            return null;
        }

        try {
            Class<?> clazz = obj.getClass();

            // Handle dot notation for nested objects (e.g., "customer.name")
            String[] parts = fieldName.split("\\.");
            Object current = obj;

            for (String part : parts) {
                if (current == null) {
                    return null;
                }

                current = getFieldValueFromObject(current, part);
            }

            return current;
        } catch (Exception e) {
            log.debug("Failed to get field value using reflection for field: {}", fieldName, e);
            return null;
        }
    }

    /**
     * Get field value from a single object, handling @Field annotations
     */
    private Object getFieldValueFromObject(Object obj, String fieldName) {
        if (obj == null || fieldName == null) {
            return null;
        }

        Class<?> clazz = obj.getClass();

        // First, try to find a field with @Field annotation that matches the requested fieldName
        for (Field field : clazz.getDeclaredFields()) {
            // Check if field has @Field annotation
            if (field.isAnnotationPresent(org.springframework.data.mongodb.core.mapping.Field.class)) {
                org.springframework.data.mongodb.core.mapping.Field fieldAnnotation =
                    field.getAnnotation(org.springframework.data.mongodb.core.mapping.Field.class);

                // Check if the annotation value matches the requested field name
                if (fieldName.equals(fieldAnnotation.value())) {
                    try {
                        field.setAccessible(true);
                        Object value = field.get(obj);
                        return convertMongoTypes(value);
                    } catch (IllegalAccessException e) {
                        log.debug("Failed to access field: {}", field.getName(), e);
                    }
                }
            }

            // Also check if the Java field name matches (fallback)
            if (fieldName.equals(field.getName())) {
                try {
                    field.setAccessible(true);
                    Object value = field.get(obj);
                    return convertMongoTypes(value);
                } catch (IllegalAccessException e) {
                    log.debug("Failed to access field: {}", field.getName(), e);
                }
            }
        }

        // Try getter methods as fallback
        try {
            String getterName = "get" + Character.toUpperCase(fieldName.charAt(0)) + fieldName.substring(1);
            java.lang.reflect.Method getter = clazz.getMethod(getterName);
            Object value = getter.invoke(obj);
            return convertMongoTypes(value);
        } catch (Exception e) {
            // Getter not found or failed
        }

        return null;
    }

    /**
     * Convert MongoDB-specific types to friendly values
     */
    private Object convertMongoTypes(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof Decimal128) {
            return ((Decimal128) value).bigDecimalValue();
        }
        if (value instanceof ObjectId) {
            return value.toString();
        }
        if (value instanceof Date) {
            return value; // Or format with SimpleDateFormat if needed
        }

        return value;
    }
}
